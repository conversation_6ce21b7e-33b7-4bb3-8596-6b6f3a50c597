import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import GreatYOPPagination from './GreatYOPPagination';


import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface CountryPageConfig {
  country: string;
  title: string;
  description: string;
  keywords: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
  flag?: string;
}

interface StandardCountryPageProps {
  config: CountryPageConfig;
}

const StandardCountryPage: React.FC<StandardCountryPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        country: config.country,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, config.country]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Hero Section - Professional Full Width */}
        <div className="bg-gradient-to-br from-gray-900 via-primary-dark to-primary text-white pt-20 pb-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
              {/* Content Section */}
              <div className="lg:col-span-8">
                <div className="flex items-center gap-4 mb-6">
                  {config.flag && (
                    <div className="text-4xl">
                      {config.flag}
                    </div>
                  )}
                  <h1 className="text-3xl md:text-5xl font-bold leading-tight">
                    Bourses d'Études en {config.country}
                  </h1>
                </div>

                <div className="space-y-4 text-lg leading-relaxed">
                  <p className="text-gray-200">
                    Découvrez les meilleures opportunités de financement pour vos études en {config.country}.
                    Notre plateforme vous donne accès à une sélection rigoureuse de bourses d'études,
                    programmes de financement et opportunités académiques adaptées à votre profil.
                  </p>

                  <p className="text-gray-300">
                    Que vous souhaitiez poursuivre un cursus de licence, master ou doctorat,
                    nous vous accompagnons dans votre recherche de financement pour concrétiser
                    vos ambitions académiques et professionnelles en {config.country}.
                  </p>
                </div>
              </div>

              {/* Stats Section */}
              <div className="lg:col-span-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <h3 className="text-xl font-semibold mb-4 text-yellow-300">
                    Pourquoi Choisir {config.country} ?
                  </h3>
                  <div className="space-y-3">
                    {config.benefits.slice(0, 4).map((benefit, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                        <span className="text-sm text-gray-200">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-6 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="w-full">{/* Full width layout without sidebar */}

              {/* Scholarships Grid */}
              <div id="scholarships-section" className="mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bourses Recommandées
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="gy-pcard-wrap">
                      {scholarships.map((scholarship, index) => (
                        <EnhancedScholarshipCard
                          key={scholarship.id}
                          id={scholarship.id}
                          title={scholarship.title}
                          thumbnail={scholarship.thumbnail}
                          deadline={scholarship.deadline}
                          isOpen={scholarship.isOpen}
                          level={scholarship.level}
                          country={scholarship.country}
                          fundingSource={scholarship.fundingSource}
                          onClick={handleScholarshipClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {pagination.total > 0 && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={pagination.total}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
              </div>
            </div>
        </div>

        {/* Newsletter Section */}
        <div className="bg-gradient-to-br from-gray-900 via-primary-dark to-primary py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                Restez Informé des Nouvelles Opportunités
              </h3>
              <p className="text-lg text-gray-200 mb-8 leading-relaxed">
                Recevez directement dans votre boîte mail les dernières bourses d'études disponibles en {config.country}
                et dans le monde entier. Ne manquez aucune opportunité de financer vos études !
              </p>

              <div className="max-w-md mx-auto">
                <div className="flex flex-col sm:flex-row gap-3 mb-4">
                  <input
                    type="email"
                    placeholder="Votre adresse email"
                    className="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  />
                  <button className="bg-yellow-400 hover:bg-yellow-300 text-blue-900 font-bold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                    S'abonner
                  </button>
                </div>
                <p className="text-xs text-gray-300">
                  Nous respectons votre vie privée. Désabonnement possible à tout moment.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default StandardCountryPage;
