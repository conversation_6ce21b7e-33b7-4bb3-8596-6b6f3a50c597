import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import GreatYOPPagination from './GreatYOPPagination';

import PageEndSuggestions from './PageEndSuggestions';
import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface CountryPageConfig {
  country: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroSubtitle: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
  flag?: string;
}

interface StandardCountryPageProps {
  config: CountryPageConfig;
}

const StandardCountryPage: React.FC<StandardCountryPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        country: config.country,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, config.country]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Hero Section - Redesigned */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <div className="flex items-center justify-center gap-4 mb-6">
                {config.flag && (
                  <div className="text-5xl">
                    {config.flag}
                  </div>
                )}
                <h1 className="text-3xl md:text-5xl font-bold leading-tight">
                  Bourses d'Études en {config.country}
                </h1>
              </div>
            </div>

            <div className="max-w-5xl mx-auto">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h2 className="text-2xl md:text-3xl font-bold mb-6 text-center text-yellow-300">
                  {config.infoTitle}
                </h2>

                <p className="text-lg md:text-xl text-blue-100 mb-8 leading-relaxed text-center">
                  {config.infoContent}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {config.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-3 bg-white/10 rounded-lg p-4">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-white font-medium">{benefit}</span>
                    </div>
                  ))}
                </div>

                <div className="text-center mt-8">
                  <button
                    onClick={() => document.getElementById('scholarships-section')?.scrollIntoView({ behavior: 'smooth' })}
                    className="bg-yellow-400 hover:bg-yellow-300 text-blue-900 font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg inline-block"
                  >
                    Découvrir les Bourses
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="w-full">{/* Full width layout without sidebar */}

              {/* Scholarships Grid */}
              <div id="scholarships-section" className="mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bourses Recommandées
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="gy-pcard-wrap">
                      {scholarships.map((scholarship, index) => (
                        <EnhancedScholarshipCard
                          key={scholarship.id}
                          id={scholarship.id}
                          title={scholarship.title}
                          thumbnail={scholarship.thumbnail}
                          deadline={scholarship.deadline}
                          isOpen={scholarship.isOpen}
                          level={scholarship.level}
                          country={scholarship.country}
                          fundingSource={scholarship.fundingSource}
                          onClick={handleScholarshipClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {pagination.total > 0 && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={pagination.total}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
              </div>

              {/* Newsletter Section */}
              <div className="mt-16 mb-8">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white text-center">
                  <h3 className="text-2xl font-bold mb-4">
                    Restez Informé des Nouvelles Bourses
                  </h3>
                  <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                    Recevez les dernières opportunités de bourses d'études directement dans votre boîte mail.
                    Ne manquez aucune chance de financer vos études !
                  </p>
                  <div className="max-w-md mx-auto">
                    <div className="flex flex-col sm:flex-row gap-3">
                      <input
                        type="email"
                        placeholder="Votre adresse email"
                        className="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                      />
                      <button className="bg-yellow-400 hover:bg-yellow-300 text-blue-900 font-bold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105">
                        S'abonner
                      </button>
                    </div>
                    <p className="text-xs text-blue-200 mt-3">
                      Nous respectons votre vie privée. Désabonnement possible à tout moment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
        </div>

        {/* Page End Suggestions */}
        <PageEndSuggestions
          currentPageType="country"
          currentItem={config.country}
        />
      </div>
    </>
  );
};

export default StandardCountryPage;
